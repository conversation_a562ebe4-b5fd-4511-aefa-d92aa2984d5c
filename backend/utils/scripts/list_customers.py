#!/usr/bin/env python
"""
Script to list all customers and their current subscription status.

This script:
1. Lists all customers from the database
2. Checks their current subscription status in Stripe
3. Shows a summary of who needs migration

Usage:
    python list_customers.py

Make sure your environment variables are properly set:
- SUPABASE_URL
- SUPABASE_SERVICE_ROLE_KEY  
- STRIPE_SECRET_KEY
"""

import asyncio
import sys
import os
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
import stripe
from datetime import datetime, timezone

# Load script-specific environment variables
load_dotenv(".env")

# Import relative modules
from services.supabase import DBConnection
from utils.logger import logger
from utils.config import config

# Initialize Stripe with the API key
stripe.api_key = config.STRIPE_SECRET_KEY

# Global database connection
db_connection = None

# Production Pro plan price ID
PRO_PRICE_ID = "price_1Ra0bGGKgx4qnTxJdHU2hODo"

async def get_all_customers() -> List[Dict[str, Any]]:
    """Query all customers from the database."""
    global db_connection
    if db_connection is None:
        db_connection = DBConnection()
    
    client = await db_connection.client
    
    # Query all customers from billing_customers
    result = await client.schema('basejump').from_('billing_customers').select(
        'id', 
        'account_id',
        'email',
        'active'
    ).execute()
    
    return result.data if result.data else []

async def get_user_info(account_id: str) -> Optional[Dict]:
    """Get user information from auth.users table."""
    try:
        global db_connection
        if db_connection is None:
            db_connection = DBConnection()
        
        client = await db_connection.client
        
        # Get user info from auth.users
        user_result = await client.auth.admin.get_user_by_id(account_id)
        if user_result and user_result.user:
            return {
                'email': user_result.user.email,
                'created_at': user_result.user.created_at,
                'last_sign_in_at': user_result.user.last_sign_in_at
            }
        return None
    except Exception as e:
        print(f"Error getting user info for {account_id}: {str(e)}")
        return None

async def check_customer_subscription_status(customer_id: str) -> Optional[Dict]:
    """Check if a customer has any active subscriptions in Stripe."""
    try:
        # List all subscriptions for this customer
        subscriptions = stripe.Subscription.list(
            customer=customer_id,
            limit=10
        )
        
        if subscriptions.data:
            # Get the most recent subscription
            subscription = subscriptions.data[0]
            return {
                'id': subscription.id,
                'status': subscription.status,
                'price_id': subscription.items.data[0].price.id if subscription.items.data else None,
                'current_period_end': subscription.current_period_end,
                'created': subscription.created
            }
        else:
            return None
            
    except stripe.error.InvalidRequestError as e:
        if "No such customer" in str(e):
            return {'error': 'customer_not_found'}
        else:
            return {'error': str(e)}
    except Exception as e:
        return {'error': str(e)}

async def main():
    """Main function to list customers."""
    print("📋 Customer List & Subscription Status")
    print("=" * 60)
    
    try:
        # Initialize global DB connection
        global db_connection
        db_connection = DBConnection()
        
        # Get all customers from the database
        customers = await get_all_customers()
        
        if not customers:
            print("No customers found in database")
            return
        
        print(f"Found {len(customers)} customers in database\n")
        
        # Process each customer
        customer_data = []
        for i, customer in enumerate(customers, 1):
            customer_id = customer['id']
            account_id = customer['account_id']
            email = customer['email']
            db_active = customer['active']
            
            print(f"Processing {i}/{len(customers)}: {email}")
            
            # Get user info
            user_info = await get_user_info(account_id)
            
            # Check Stripe subscription
            subscription_info = await check_customer_subscription_status(customer_id)
            
            customer_data.append({
                'customer_id': customer_id,
                'account_id': account_id,
                'email': email,
                'db_active': db_active,
                'user_info': user_info,
                'subscription_info': subscription_info
            })
        
        # Print detailed summary
        print("\n" + "=" * 60)
        print("📊 DETAILED CUSTOMER SUMMARY")
        print("=" * 60)
        
        needs_migration = []
        has_pro = []
        has_other = []
        errors = []
        
        for data in customer_data:
            email = data['email']
            customer_id = data['customer_id'][:20] + "..."
            db_active = "✅" if data['db_active'] else "❌"
            
            subscription = data['subscription_info']
            
            if subscription is None:
                status = "❌ No subscription"
                needs_migration.append(data)
            elif 'error' in subscription:
                status = f"⚠️ Error: {subscription['error']}"
                errors.append(data)
            elif subscription['status'] == 'active' and subscription['price_id'] == PRO_PRICE_ID:
                status = "✅ Pro (Active)"
                has_pro.append(data)
            elif subscription['status'] == 'active':
                status = f"🔄 Active ({subscription['price_id'][:20]}...)"
                has_other.append(data)
            else:
                status = f"⏸️ {subscription['status'].title()}"
                needs_migration.append(data)
            
            # User last activity
            last_activity = "Never"
            if data['user_info'] and data['user_info']['last_sign_in_at']:
                last_signin = datetime.fromisoformat(data['user_info']['last_sign_in_at'].replace('Z', '+00:00'))
                days_ago = (datetime.now(timezone.utc) - last_signin).days
                last_activity = f"{days_ago} days ago"
            
            print(f"{email:<35} | DB: {db_active} | Stripe: {status:<25} | Last: {last_activity}")
        
        # Print migration summary
        print("\n" + "=" * 60)
        print("🎯 MIGRATION SUMMARY")
        print("=" * 60)
        print(f"✅ Already have Pro plan: {len(has_pro)}")
        print(f"🔄 Have other active subscription: {len(has_other)}")
        print(f"🚀 Need migration to Pro: {len(needs_migration)}")
        print(f"⚠️ Errors/Issues: {len(errors)}")
        
        if needs_migration:
            print(f"\n📝 CUSTOMERS NEEDING MIGRATION ({len(needs_migration)}):")
            for data in needs_migration:
                print(f"- {data['email']}")
        
        if has_other:
            print(f"\n🔄 CUSTOMERS WITH OTHER SUBSCRIPTIONS ({len(has_other)}):")
            for data in has_other:
                sub = data['subscription_info']
                print(f"- {data['email']} (Price: {sub['price_id']}, Status: {sub['status']})")
        
        if errors:
            print(f"\n⚠️ CUSTOMERS WITH ERRORS ({len(errors)}):")
            for data in errors:
                error = data['subscription_info']['error']
                print(f"- {data['email']}: {error}")
        
        print(f"\nTotal customers: {len(customer_data)}")
        
    except Exception as e:
        logger.error(f"Error listing customers: {str(e)}")
        print(f"❌ Error: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
