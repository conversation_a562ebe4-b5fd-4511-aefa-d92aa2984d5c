#!/usr/bin/env python
"""
<PERSON>ript to migrate existing beta customers to Pro plan.

This script:
1. Lists all existing customers from the database
2. Checks their current subscription status in Stripe
3. Creates Pro subscriptions for customers without active subscriptions
4. Updates customer status in the database

Usage:
    python migrate_beta_customers_to_pro.py

Make sure your environment variables are properly set:
- SUPABASE_URL
- SUPABASE_SERVICE_ROLE_KEY  
- STRIPE_SECRET_KEY
"""

import asyncio
import sys
import os
import time
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
import stripe
from datetime import datetime, timezone

# Load script-specific environment variables
load_dotenv(".env")

# Import relative modules
from services.supabase import DBConnection
from utils.logger import logger
from utils.config import config

# Initialize Stripe with the API key
stripe.api_key = config.STRIPE_SECRET_KEY

# Global database connection
db_connection = None

# Production Pro plan price ID
PRO_PRICE_ID = "price_1Ra0bGGKgx4qnTxJdHU2hODo"
PRODUCT_ID = "prod_SV0cYYCWXJqSmO"

async def get_all_customers() -> List[Dict[str, Any]]:
    """
    Query all customers from the database.
    
    Returns:
        List of customers with their ID, account_id, email, and active status
    """
    global db_connection
    if db_connection is None:
        db_connection = DBConnection()
    
    client = await db_connection.client
    
    print(f"Using Supabase URL: {os.getenv('SUPABASE_URL')}")
    
    # Query all customers from billing_customers
    result = await client.schema('basejump').from_('billing_customers').select(
        'id', 
        'account_id',
        'email',
        'active'
    ).execute()
    
    print(f"Found {len(result.data)} customers in database")
    
    if not result.data:
        logger.info("No customers found in database")
        return []
    
    return result.data

async def check_customer_subscription_status(customer_id: str) -> Optional[Dict]:
    """
    Check if a customer has any active subscriptions in Stripe.
    
    Args:
        customer_id: Stripe customer ID
        
    Returns:
        Dict with subscription info if active, None if no active subscription
    """
    try:
        print(f"Checking Stripe subscriptions for customer: {customer_id}")
        
        # List all subscriptions for this customer
        subscriptions = stripe.Subscription.list(
            customer=customer_id,
            status='active',
            limit=10
        )
        
        if subscriptions.data:
            # Return the first active subscription
            subscription = subscriptions.data[0]
            print(f"✅ Customer {customer_id} has active subscription: {subscription.id}")
            return {
                'id': subscription.id,
                'status': subscription.status,
                'price_id': subscription.items.data[0].price.id if subscription.items.data else None,
                'current_period_end': subscription.current_period_end
            }
        else:
            print(f"❌ Customer {customer_id} has NO active subscription")
            return None
            
    except stripe.error.InvalidRequestError as e:
        if "No such customer" in str(e):
            print(f"⚠️ Customer {customer_id} not found in Stripe")
            return None
        else:
            print(f"❌ Error checking customer {customer_id}: {str(e)}")
            return None
    except Exception as e:
        print(f"❌ Unexpected error checking customer {customer_id}: {str(e)}")
        return None

async def create_pro_subscription(customer_id: str, email: str) -> Optional[str]:
    """
    Create a Pro subscription for a customer.
    
    Args:
        customer_id: Stripe customer ID
        email: Customer email
        
    Returns:
        Subscription ID if successful, None if failed
    """
    try:
        print(f"Creating Pro subscription for customer: {customer_id} ({email})")
        
        # Create subscription with Pro price
        subscription = stripe.Subscription.create(
            customer=customer_id,
            items=[{
                'price': PRO_PRICE_ID,
            }],
            metadata={
                'migration': 'beta_to_pro',
                'migration_date': datetime.now(timezone.utc).isoformat()
            },
            # Give them a trial period to ease the transition
            trial_period_days=30,  # 30 days free trial
        )
        
        print(f"✅ Created Pro subscription {subscription.id} for customer {customer_id}")
        return subscription.id
        
    except Exception as e:
        print(f"❌ Error creating subscription for customer {customer_id}: {str(e)}")
        return None

async def update_customer_active_status(customer_id: str, active: bool) -> bool:
    """
    Update customer active status in the database.
    
    Args:
        customer_id: Stripe customer ID
        active: Active status to set
        
    Returns:
        True if successful, False if failed
    """
    try:
        global db_connection
        if db_connection is None:
            db_connection = DBConnection()
        
        client = await db_connection.client
        
        result = await client.schema('basejump').from_('billing_customers').update(
            {'active': active}
        ).eq('id', customer_id).execute()
        
        print(f"✅ Updated customer {customer_id} active status to {active}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating customer {customer_id} status: {str(e)}")
        return False

async def migrate_customer_to_pro(customer: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migrate a single customer to Pro plan.
    
    Args:
        customer: Customer data from database
        
    Returns:
        Migration result dictionary
    """
    customer_id = customer['id']
    email = customer['email']
    account_id = customer['account_id']
    
    result = {
        'customer_id': customer_id,
        'email': email,
        'account_id': account_id,
        'status': 'pending',
        'subscription_id': None,
        'error': None
    }
    
    try:
        # Check current subscription status
        current_subscription = await check_customer_subscription_status(customer_id)
        
        if current_subscription:
            # Customer already has an active subscription
            if current_subscription['price_id'] == PRO_PRICE_ID:
                result['status'] = 'already_pro'
                result['subscription_id'] = current_subscription['id']
                print(f"✅ Customer {customer_id} already has Pro subscription")
            else:
                result['status'] = 'has_other_subscription'
                result['subscription_id'] = current_subscription['id']
                print(f"ℹ️ Customer {customer_id} has different subscription: {current_subscription['price_id']}")
        else:
            # Customer has no active subscription, create Pro subscription
            subscription_id = await create_pro_subscription(customer_id, email)
            
            if subscription_id:
                # Update customer status to active
                await update_customer_active_status(customer_id, True)
                result['status'] = 'migrated_to_pro'
                result['subscription_id'] = subscription_id
                print(f"✅ Successfully migrated customer {customer_id} to Pro")
            else:
                result['status'] = 'failed_to_create_subscription'
                result['error'] = 'Could not create Pro subscription'
                
    except Exception as e:
        result['status'] = 'error'
        result['error'] = str(e)
        print(f"❌ Error migrating customer {customer_id}: {str(e)}")
    
    return result

async def main():
    """Main migration function."""
    print("🚀 Starting Beta Customer to Pro Migration")
    print("=" * 50)
    
    try:
        # Initialize global DB connection
        global db_connection
        db_connection = DBConnection()
        
        # Get all customers from the database
        customers = await get_all_customers()
        
        if not customers:
            logger.info("No customers to migrate")
            return
        
        print(f"\nFound {len(customers)} customers to process")
        print("\nCustomer Summary:")
        for i, customer in enumerate(customers, 1):
            print(f"{i}. {customer['email']} (ID: {customer['id'][:20]}...)")
        
        # Ask for confirmation before proceeding
        confirm = input(f"\nProceed with migrating {len(customers)} customers to Pro plan? (y/n): ")
        if confirm.lower() != 'y':
            logger.info("Migration cancelled by user")
            return
        
        # Process each customer
        results = []
        for i, customer in enumerate(customers, 1):
            print(f"\n--- Processing Customer {i}/{len(customers)} ---")
            result = await migrate_customer_to_pro(customer)
            results.append(result)
            
            # Add a small delay to avoid rate limiting
            time.sleep(0.5)
        
        # Print summary
        print("\n" + "=" * 50)
        print("🎉 MIGRATION SUMMARY")
        print("=" * 50)
        
        status_counts = {}
        for result in results:
            status = result['status']
            status_counts[status] = status_counts.get(status, 0) + 1
        
        for status, count in status_counts.items():
            print(f"{status.replace('_', ' ').title()}: {count}")
        
        print(f"\nTotal customers processed: {len(results)}")
        
        # Show detailed results for failed migrations
        failed_results = [r for r in results if r['status'] in ['error', 'failed_to_create_subscription']]
        if failed_results:
            print(f"\n⚠️ FAILED MIGRATIONS ({len(failed_results)}):")
            for result in failed_results:
                print(f"- {result['email']}: {result['error']}")
        
        logger.info("Beta customer migration completed")
        
    except Exception as e:
        logger.error(f"Migration failed: {str(e)}")
        print(f"❌ Migration failed: {str(e)}")

if __name__ == "__main__":
    asyncio.run(main())
