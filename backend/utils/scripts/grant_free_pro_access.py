#!/usr/bin/env python
"""
Script to grant free Pro access to beta customers without charging them.

This script creates Pro subscriptions with extended trial periods or
100% discount coupons for existing beta customers.

Usage:
    python grant_free_pro_access.py

Options:
    --trial-days: Number of trial days (default: 365 for 1 year free)
    --permanent: Create permanent free subscriptions using 100% discount

Make sure your environment variables are properly set:
- SUPABASE_URL
- SUPABASE_SERVICE_ROLE_KEY
- STRIPE_SECRET_KEY
"""

import asyncio
import sys
import os
import argparse
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv
import stripe
from datetime import datetime, timezone

# Add the backend directory to Python path
backend_dir = os.path.dirname(
    os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
)
sys.path.insert(0, backend_dir)

# Load script-specific environment variables
load_dotenv(os.path.join(backend_dir, ".env"))

# Import relative modules
from services.supabase import DBConnection
from utils.logger import logger
from utils.config import config

# Initialize Stripe with the API key
stripe.api_key = config.STRIPE_SECRET_KEY

# Global database connection
db_connection = None

# Production Pro plan price ID
PRO_PRICE_ID = "price_1Ra0bGGKgx4qnTxJdHU2hODo"
PRODUCT_ID = "prod_SV0cYYCWXJqSmO"


async def get_customers_needing_migration() -> List[Dict[str, Any]]:
    """Get customers who need migration (no active Pro subscription)."""
    global db_connection
    if db_connection is None:
        db_connection = DBConnection()

    client = await db_connection.client

    # Query all customers from billing_customers
    result = (
        await client.schema("basejump")
        .from_("billing_customers")
        .select("id", "account_id", "email", "active")
        .execute()
    )

    if not result.data:
        return []

    # Filter customers who need migration
    customers_needing_migration = []
    for customer in result.data:
        # Check if they have an active Pro subscription
        try:
            subscriptions = stripe.Subscription.list(
                customer=customer["id"], status="active", limit=10
            )

            has_pro = any(
                sub.items.data[0].price.id == PRO_PRICE_ID
                for sub in subscriptions.data
                if sub.items.data
            )

            if not has_pro:
                customers_needing_migration.append(customer)

        except stripe.error.InvalidRequestError:
            # Customer not found in Stripe, include them for migration
            customers_needing_migration.append(customer)

    return customers_needing_migration


async def create_free_pro_subscription(
    customer_id: str, email: str, trial_days: int = 365, permanent: bool = False
) -> Optional[str]:
    """
    Create a free Pro subscription for a customer.

    Args:
        customer_id: Stripe customer ID
        email: Customer email
        trial_days: Number of trial days (ignored if permanent=True)
        permanent: If True, create permanent free subscription with 100% discount

    Returns:
        Subscription ID if successful, None if failed
    """
    try:
        print(f"Creating free Pro subscription for: {email}")

        if permanent:
            # Create a 100% discount coupon for permanent free access
            coupon = stripe.Coupon.create(
                percent_off=100,
                duration="forever",
                name=f"Beta Customer Free Pro - {email}",
                metadata={
                    "type": "beta_customer_free_pro",
                    "customer_email": email,
                    "created_date": datetime.now(timezone.utc).isoformat(),
                },
            )

            # Create subscription with 100% discount
            subscription = stripe.Subscription.create(
                customer=customer_id,
                items=[
                    {
                        "price": PRO_PRICE_ID,
                    }
                ],
                coupon=coupon.id,
                metadata={
                    "migration": "beta_to_free_pro",
                    "migration_date": datetime.now(timezone.utc).isoformat(),
                    "type": "permanent_free",
                },
            )
            print(
                f"✅ Created permanent free Pro subscription {subscription.id} for {email}"
            )

        else:
            # Create subscription with extended trial period
            subscription = stripe.Subscription.create(
                customer=customer_id,
                items=[
                    {
                        "price": PRO_PRICE_ID,
                    }
                ],
                trial_period_days=trial_days,
                metadata={
                    "migration": "beta_to_free_pro",
                    "migration_date": datetime.now(timezone.utc).isoformat(),
                    "type": "extended_trial",
                    "trial_days": str(trial_days),
                },
            )
            print(
                f"✅ Created {trial_days}-day trial Pro subscription {subscription.id} for {email}"
            )

        return subscription.id

    except Exception as e:
        print(f"❌ Error creating free subscription for {email}: {str(e)}")
        return None


async def update_customer_active_status(customer_id: str, active: bool = True) -> bool:
    """Update customer active status in the database."""
    try:
        global db_connection
        if db_connection is None:
            db_connection = DBConnection()

        client = await db_connection.client

        result = (
            await client.schema("basejump")
            .from_("billing_customers")
            .update({"active": active})
            .eq("id", customer_id)
            .execute()
        )

        return True

    except Exception as e:
        print(f"❌ Error updating customer {customer_id} status: {str(e)}")
        return False


async def grant_free_access_to_customer(
    customer: Dict[str, Any], trial_days: int, permanent: bool
) -> Dict[str, Any]:
    """Grant free Pro access to a single customer."""
    customer_id = customer["id"]
    email = customer["email"]

    result = {
        "customer_id": customer_id,
        "email": email,
        "status": "pending",
        "subscription_id": None,
        "error": None,
    }

    try:
        # Create free Pro subscription
        subscription_id = await create_free_pro_subscription(
            customer_id, email, trial_days, permanent
        )

        if subscription_id:
            # Update customer status to active
            await update_customer_active_status(customer_id, True)
            result["status"] = "granted_free_pro"
            result["subscription_id"] = subscription_id
        else:
            result["status"] = "failed_to_create_subscription"
            result["error"] = "Could not create free Pro subscription"

    except Exception as e:
        result["status"] = "error"
        result["error"] = str(e)
        print(f"❌ Error granting free access to {email}: {str(e)}")

    return result


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Grant free Pro access to beta customers"
    )
    parser.add_argument(
        "--trial-days",
        type=int,
        default=365,
        help="Number of trial days (default: 365)",
    )
    parser.add_argument(
        "--permanent",
        action="store_true",
        help="Create permanent free subscriptions with 100%% discount",
    )

    args = parser.parse_args()

    print("🎁 Granting Free Pro Access to Beta Customers")
    print("=" * 50)

    if args.permanent:
        print("Mode: Permanent free access (100% discount)")
    else:
        print(f"Mode: Extended trial ({args.trial_days} days)")

    try:
        # Initialize global DB connection
        global db_connection
        db_connection = DBConnection()

        # Get customers needing migration
        customers = await get_customers_needing_migration()

        if not customers:
            print("No customers need migration - all already have Pro access!")
            return

        print(f"\nFound {len(customers)} customers needing free Pro access:")
        for i, customer in enumerate(customers, 1):
            print(f"{i}. {customer['email']}")

        # Ask for confirmation
        access_type = (
            "permanent free access"
            if args.permanent
            else f"{args.trial_days}-day trial"
        )
        confirm = input(f"\nGrant {access_type} to {len(customers)} customers? (y/n): ")
        if confirm.lower() != "y":
            print("Operation cancelled")
            return

        # Process each customer
        results = []
        for i, customer in enumerate(customers, 1):
            print(f"\n--- Processing {i}/{len(customers)}: {customer['email']} ---")
            result = await grant_free_access_to_customer(
                customer, args.trial_days, args.permanent
            )
            results.append(result)

        # Print summary
        print("\n" + "=" * 50)
        print("🎉 FREE ACCESS GRANT SUMMARY")
        print("=" * 50)

        successful = [r for r in results if r["status"] == "granted_free_pro"]
        failed = [r for r in results if r["status"] != "granted_free_pro"]

        print(f"✅ Successfully granted: {len(successful)}")
        print(f"❌ Failed: {len(failed)}")

        if successful:
            print(f"\n✅ SUCCESSFUL GRANTS ({len(successful)}):")
            for result in successful:
                print(f"- {result['email']}: {result['subscription_id']}")

        if failed:
            print(f"\n❌ FAILED GRANTS ({len(failed)}):")
            for result in failed:
                print(f"- {result['email']}: {result['error']}")

        print(f"\nTotal processed: {len(results)}")

    except Exception as e:
        logger.error(f"Error granting free access: {str(e)}")
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
