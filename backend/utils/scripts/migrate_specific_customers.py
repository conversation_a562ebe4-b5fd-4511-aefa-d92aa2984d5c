#!/usr/bin/env python
"""
<PERSON><PERSON>t to migrate specific customers to the new Pro plan.

This script migrates <PERSON><PERSON> and <PERSON> to the new production Pro plan.
"""

import asyncio
import sys
import os
from dotenv import load_dotenv
import stripe
from datetime import datetime, timezone

# Add the backend directory to Python path
backend_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, backend_dir)

# Load script-specific environment variables
load_dotenv(os.path.join(backend_dir, ".env"))

# Import relative modules
from services.supabase import DBConnection
from utils.logger import logger
from utils.config import config

# Initialize Stripe with the API key
stripe.api_key = config.STRIPE_SECRET_KEY

# Production Pro plan price ID
PRO_PRICE_ID = "price_1Ra0bGGKgx4qnTxJdHU2hODo"

# Customer data
CUSTOMERS = [
    {
        "customer_id": "cus_SRfNhFRC8cQbeB",
        "name": "<PERSON><PERSON>",
        "email": "<EMAIL>"
    },
    {
        "customer_id": "cus_SLXL3KpNtSYYYN", 
        "name": "<PERSON>",
        "email": "<EMAIL>"
    }
]

async def get_customer_subscription(customer_id: str):
    """Get current subscription for a customer."""
    try:
        subscriptions = stripe.Subscription.list(
            customer=customer_id,
            status='active',
            limit=10
        )
        
        if subscriptions.data:
            return subscriptions.data[0]
        return None
        
    except Exception as e:
        print(f"Error getting subscription for {customer_id}: {str(e)}")
        return None

async def update_subscription_to_pro(customer_id: str, subscription_id: str, customer_name: str):
    """Update existing subscription to Pro plan."""
    try:
        print(f"Updating {customer_name}'s subscription to Pro plan...")
        
        # Get the subscription
        subscription = stripe.Subscription.retrieve(subscription_id)
        
        if not subscription.items.data:
            print(f"No subscription items found for {customer_name}")
            return False
        
        # Update the subscription to use the new Pro price
        updated_subscription = stripe.Subscription.modify(
            subscription_id,
            items=[{
                'id': subscription.items.data[0].id,
                'price': PRO_PRICE_ID,
            }],
            proration_behavior='none',  # No proration since we're giving them a better deal
            metadata={
                'migration': 'beta_to_production_pro',
                'migration_date': datetime.now(timezone.utc).isoformat(),
                'previous_price': subscription.items.data[0].price.id
            }
        )
        
        print(f"✅ Successfully updated {customer_name} to Pro plan")
        print(f"   Subscription ID: {updated_subscription.id}")
        print(f"   New Price ID: {PRO_PRICE_ID}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating {customer_name}'s subscription: {str(e)}")
        return False

async def ensure_customer_in_database(customer_data):
    """Ensure customer exists in our database."""
    try:
        db = DBConnection()
        client = await db.client
        
        # Check if customer exists
        result = await client.schema('basejump').from_('billing_customers').select('*').eq('id', customer_data['customer_id']).execute()
        
        if result.data:
            print(f"✅ {customer_data['name']} already exists in database")
            return True
        else:
            print(f"⚠️ {customer_data['name']} not found in database - this might be expected")
            # You might want to add them to the database here if needed
            return True
            
    except Exception as e:
        print(f"❌ Error checking database for {customer_data['name']}: {str(e)}")
        return False

async def update_customer_active_status(customer_id: str):
    """Update customer active status in database."""
    try:
        db = DBConnection()
        client = await db.client
        
        result = await client.schema('basejump').from_('billing_customers').update(
            {'active': True}
        ).eq('id', customer_id).execute()
        
        return True
        
    except Exception as e:
        print(f"Error updating customer status: {str(e)}")
        return False

async def migrate_customer(customer_data):
    """Migrate a single customer to Pro plan."""
    customer_id = customer_data['customer_id']
    name = customer_data['name']
    email = customer_data['email']
    
    print(f"\n--- Migrating {name} ({email}) ---")
    
    # Ensure customer is in database
    await ensure_customer_in_database(customer_data)
    
    # Get current subscription
    subscription = await get_customer_subscription(customer_id)
    
    if not subscription:
        print(f"❌ No active subscription found for {name}")
        return False
    
    print(f"Current subscription: {subscription.id}")
    print(f"Current price: {subscription.items.data[0].price.id}")
    print(f"Current amount: ${subscription.items.data[0].price.unit_amount / 100}")
    
    # Check if already on Pro plan
    if subscription.items.data[0].price.id == PRO_PRICE_ID:
        print(f"✅ {name} is already on the Pro plan")
        return True
    
    # Update to Pro plan
    success = await update_subscription_to_pro(customer_id, subscription.id, name)
    
    if success:
        # Update database status
        await update_customer_active_status(customer_id)
        print(f"✅ {name} successfully migrated to Pro plan")
    
    return success

async def main():
    """Main migration function."""
    print("🚀 Migrating Specific Customers to Production Pro Plan")
    print("=" * 60)
    
    print(f"Target Pro Plan: {PRO_PRICE_ID}")
    print(f"Customers to migrate: {len(CUSTOMERS)}")
    
    for customer in CUSTOMERS:
        print(f"- {customer['name']} ({customer['email']})")
    
    # Ask for confirmation
    confirm = input(f"\nProceed with migration? (y/n): ")
    if confirm.lower() != 'y':
        print("Migration cancelled")
        return
    
    # Migrate each customer
    results = []
    for customer in CUSTOMERS:
        success = await migrate_customer(customer)
        results.append({
            'customer': customer,
            'success': success
        })
    
    # Print summary
    print("\n" + "=" * 60)
    print("🎉 MIGRATION SUMMARY")
    print("=" * 60)
    
    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]
    
    print(f"✅ Successfully migrated: {len(successful)}")
    print(f"❌ Failed migrations: {len(failed)}")
    
    if successful:
        print(f"\n✅ SUCCESSFUL MIGRATIONS:")
        for result in successful:
            customer = result['customer']
            print(f"- {customer['name']} ({customer['email']})")
    
    if failed:
        print(f"\n❌ FAILED MIGRATIONS:")
        for result in failed:
            customer = result['customer']
            print(f"- {customer['name']} ({customer['email']})")
    
    print(f"\nAll customers are now on the production Pro plan!")

if __name__ == "__main__":
    asyncio.run(main())
